// Package features provides unified distribution storage for Mulberri decision trees.
//
// Uses a single Distribution structure that maintains sorted order by value across
// all feature types (string, integer, float). Eliminates type conversion overhead
// and provides consistent operations for C4.5 split evaluation.
//
// Security: No sensitive data storage, thread-safe for concurrent read access
// Performance: O(n) insertions with sorted maintenance, O(log n) range queries
package features

import (
	"fmt"
	"math"
	"strings"

	"github.com/berrijam/mulberri/internal/utils/logger"
)

// FeatureType represents the core feature types supported by Mulberri.
//
// Args: None (enum type)
// Constraints: Must be one of IntegerFeature, FloatFeature, StringFeature
// Security: No sensitive data handling
// Relationships: Maps to handle_as values from YAML configuration
// Side effects: Determines internal data processing and storage types
type FeatureType int

const (
	IntegerFeature FeatureType = iota // Converted to int64 internally
	FloatFeature                      // Converted to float64 internally
	StringFeature                     // Stored as string internally
)

// String returns the string representation of FeatureType.
//
// Args: None (method receiver)
//
// Returns: string representation of feature type
// Security: No sensitive data exposure
func (ft FeatureType) String() string {
	switch ft {
	case IntegerFeature:
		return "integer"
	case FloatFeature:
		return "float"
	case StringFeature:
		return "string"
	default:
		return "unknown"
	}
}

// ParseFeatureType converts a handle_as string from YAML configuration to FeatureType enum.
//
// Args:
// - handleAs: String value from YAML handle_as field ("integer", "float", "string")
//
// Returns:
// - FeatureType: Corresponding enum value for internal processing
// - error: Error if handleAs value is not supported
//
// Security: Validates input against supported types
// Performance: O(1) string comparison
// Relationships: Used during YAML configuration parsing for type conversion
// Side effects: None (pure conversion function)
//
// Examples:
//
//	ParseFeatureType("integer") -> IntegerFeature, nil
//	ParseFeatureType("float") -> FloatFeature, nil
//	ParseFeatureType("string") -> StringFeature, nil
//	ParseFeatureType("invalid") -> StringFeature, error
func ParseFeatureType(handleAs string) (FeatureType, error) {
	normalizedHandleAs := strings.ToLower(strings.TrimSpace(handleAs))

	switch normalizedHandleAs {
	case "integer":
		return IntegerFeature, nil
	case "float":
		return FloatFeature, nil
	case "string":
		return StringFeature, nil
	default:
		return StringFeature, fmt.Errorf("unsupported handle_as value '%s', must be one of: integer, float, string", handleAs)
	}
}

// ValueEntry represents a single value and its count in the distribution.
//
// Args: Stores comparable values with occurrence counts
// Constraints: Value must be int64, float64, or string for proper comparison
// Performance: Efficient storage for value-count pairs
// Relationships: Used by Distribution to maintain sorted value-count pairs
// Side effects: Stores actual typed values without conversion overhead
type ValueEntry struct {
	Value interface{} // The actual value (int64, float64, or string)
	Count int         // Number of occurrences
}

// Distribution provides ordered access to feature values with counts across all types.
//
// Args: Maintains sorted entries by value regardless of feature type
// Constraints: Supports int64, float64, and string values only
// Security: No sensitive data storage
// Performance: O(n) insertions to maintain sort order, O(log n) searches
// Relationships: Used by FeatureInfo for all feature types uniformly
// Side effects: Maintains sorted order by value, allocates memory for entries
//
// Replaces the problematic map[interface{}]int approach and eliminates
// float precision issues while providing consistent operations across all types.
type Distribution struct {
	featureType FeatureType  // Feature type for proper value comparison
	entries     []ValueEntry // Maintained in sorted order by value
	totalCount  int          // Cached total count for performance
}

// NewDistribution creates a new distribution for the given feature type.
//
// Args:
// - featureType: Type of values this distribution will store (IntegerFeature/FloatFeature/StringFeature)
//
// Returns: initialized Distribution with empty entries and zero count
// Performance: O(1) initialization with zero memory allocations for entries
// Relationships: Used by NewFeatureInfo to create type-appropriate distributions
// Side effects: Allocates memory for entry slice structure
func NewDistribution(featureType FeatureType) *Distribution {
	return &Distribution{
		featureType: featureType,
		entries:     make([]ValueEntry, 0),
		totalCount:  0,
	}
}

// AddValue increments the count for a value, maintaining sorted order by value.
//
// Args:
// - value: Value to add (must match distribution's featureType)
//
// Performance: O(log n) binary search + O(n) insertion to maintain sorted order
// Relationships: Called during dataset loading for each feature sample
// Side effects: Updates entries slice and total count, may trigger memory reallocation
//
// Example: dist.AddValue(int64(25)) or dist.AddValue("red")
func (d *Distribution) AddValue(value interface{}) {
	// Validate value type matches distribution type
	if !d.isValidValueType(value) {
		logger.Fatal(fmt.Sprintf("value type %T does not match distribution feature type %v", value, d.featureType))
		return
	}

	// Find insertion point using binary search
	idx := d.findInsertionPoint(value)

	// Check if value already exists at this position
	if idx < len(d.entries) && d.compareValues(d.entries[idx].Value, value) == 0 {
		// Value exists, increment count
		d.entries[idx].Count++
	} else {
		// Insert new entry maintaining sorted order
		newEntry := ValueEntry{Value: value, Count: 1}

		// Grow slice and shift elements to make room
		d.entries = append(d.entries, ValueEntry{})
		copy(d.entries[idx+1:], d.entries[idx:])
		d.entries[idx] = newEntry
	}

	d.totalCount++
}

// GetCount returns the count for a specific value.
//
// Args:
// - value: Value to lookup (must match distribution's featureType)
//
// Returns: count of occurrences, 0 if value not found or wrong type
// Performance: O(log n) binary search to locate value
// Relationships: Used by split evaluation algorithms for count queries
func (d *Distribution) GetCount(value interface{}) int {
	if !d.isValidValueType(value) {
		return 0
	}

	idx := d.findValue(value)
	if idx >= 0 {
		return d.entries[idx].Count
	}
	return 0
}

// GetEntries returns all entries in sorted order by value.
//
// Args: None (method receiver)
//
// Returns: copy of entries slice (safe for external modification)
// Performance: O(n) copy operation to prevent external modification
// Relationships: Used for iteration over all values in sorted order
// Side effects: Creates defensive copy to protect internal state
func (d *Distribution) GetEntries() []ValueEntry {
	result := make([]ValueEntry, len(d.entries))
	copy(result, d.entries)
	return result
}

// GetRangeCount returns the sum of counts for values in the specified index range [startIdx, endIdx).
//
// Args:
// - startIdx: Starting index (inclusive, must be >= 0)
// - endIdx: Ending index (exclusive, must be <= len(entries))
//
// Returns: sum of counts in specified range, 0 for invalid ranges
// Constraints: startIdx must be < endIdx and both within bounds
// Performance: O(endIdx - startIdx) linear summation over range
// Relationships: Used by range query operations for numerical split evaluation
func (d *Distribution) GetRangeCount(startIdx, endIdx int) int {
	if startIdx < 0 || endIdx > len(d.entries) || startIdx >= endIdx {
		return 0
	}

	count := 0
	for i := startIdx; i < endIdx; i++ {
		count += d.entries[i].Count
	}
	return count
}

// FindSplitPoint returns the index of the first element >= splitValue for binary searches.
//
// Args:
// - splitValue: Value to search for (must match distribution's featureType)
//
// Returns: index of first element >= splitValue, len(entries) if all elements are smaller
// Performance: O(log n) binary search over sorted entries
// Relationships: Used by split evaluation to find threshold positions
// Side effects: None (read-only binary search operation)
//
// Example: For values [10, 20, 30], FindSplitPoint(25) returns index 2 (position of 30)
func (d *Distribution) FindSplitPoint(splitValue interface{}) int {
	if !d.isValidValueType(splitValue) {
		return len(d.entries)
	}

	left, right := 0, len(d.entries)

	for left < right {
		mid := (left + right) / 2
		if d.compareValues(d.entries[mid].Value, splitValue) < 0 {
			left = mid + 1
		} else {
			right = mid
		}
	}

	return left
}

// GetSplitCounts returns counts for left and right sides of a split at given index.
//
// Args:
// - splitIdx: Index position to split at (elements [0, splitIdx) go left, [splitIdx, end) go right)
//
// Returns:
// - leftCount: sum of counts for indices [0, splitIdx)
// - rightCount: sum of counts for indices [splitIdx, len(entries))
//
// Performance: O(n) summation over left and right ranges
// Relationships: Used by C4.5 split evaluation to calculate split quality
// Side effects: None (read-only count operations)
//
// Example: For split at index 2, returns (count[0]+count[1], count[2]+count[3]+...)
func (d *Distribution) GetSplitCounts(splitIdx int) (leftCount, rightCount int) {
	leftCount = d.GetRangeCount(0, splitIdx)
	rightCount = d.GetRangeCount(splitIdx, len(d.entries))
	return leftCount, rightCount
}

// TotalCount returns the total number of values in the distribution.
//
// Args: None (method receiver)
//
// Returns: total count of all values (cached for performance)
// Performance: O(1) using cached counter
// Relationships: Used for statistical calculations and validation
func (d *Distribution) TotalCount() int {
	return d.totalCount
}

// UniqueValues returns the number of unique values in the distribution.
//
// Args: None (method receiver)
//
// Returns: count of unique values (length of entries slice)
// Performance: O(1) using slice length
// Relationships: Used for feature cardinality analysis
func (d *Distribution) UniqueValues() int {
	return len(d.entries)
}

// GetMostCommonValue returns the most frequently occurring value and its count.
//
// Args: None (method receiver)
//
// Returns:
// - interface{}: most common value (nil if no values recorded)
// - int: count of occurrences (0 if no values recorded)
//
// Performance: O(n) iteration through entries to find maximum count
// Relationships: Used for missing value imputation strategies
// Side effects: None (read-only operation)
func (d *Distribution) GetMostCommonValue() (interface{}, int) {
	var mostCommon interface{}
	maxCount := 0

	for _, entry := range d.entries {
		if entry.Count > maxCount {
			maxCount = entry.Count
			mostCommon = entry.Value
		}
	}

	return mostCommon, maxCount
}

// findInsertionPoint uses binary search to find where a value should be inserted to maintain sort order.
//
// Args:
// - value: Value to find insertion point for
//
// Returns: index where value should be inserted to maintain sorted order
// Performance: O(log n) binary search over sorted entries
// Relationships: Used internally by AddValue to maintain sorted order
// Side effects: None (read-only binary search)
func (d *Distribution) findInsertionPoint(value interface{}) int {
	left, right := 0, len(d.entries)

	for left < right {
		mid := (left + right) / 2
		if d.compareValues(d.entries[mid].Value, value) < 0 {
			left = mid + 1
		} else {
			right = mid
		}
	}

	return left
}

// findValue returns the index of a value using binary search, or -1 if not found.
//
// Args:
// - value: Value to search for
//
// Returns: index of value if found, -1 if not found
// Performance: O(log n) binary search over sorted entries
// Relationships: Used internally by GetCount for efficient value lookup
// Side effects: None (read-only binary search)
func (d *Distribution) findValue(value interface{}) int {
	idx := d.findInsertionPoint(value)
	if idx < len(d.entries) && d.compareValues(d.entries[idx].Value, value) == 0 {
		return idx
	}
	return -1
}

// isValidValueType validates that a value matches the distribution's feature type.
//
// Args:
// - value: Value to validate against feature type
//
// Returns: true if value type matches feature type, false otherwise
// Performance: O(1) type assertion checks
// Relationships: Used by all value operations to ensure type safety
// Side effects: None (read-only type checking)
func (d *Distribution) isValidValueType(value interface{}) bool {
	switch d.featureType {
	case IntegerFeature:
		_, ok := value.(int64)
		return ok
	case FloatFeature:
		_, ok := value.(float64)
		return ok
	case StringFeature:
		_, ok := value.(string)
		return ok
	default:
		return false
	}
}

// compareValues compares two values according to the feature type for sorting.
//
// Args:
// - a: First value to compare
// - b: Second value to compare
//
// Returns: -1 if a < b, 0 if a == b, 1 if a > b
// Performance: O(1) type-specific comparison operations
// Relationships: Used by all sorting and searching operations
// Side effects: None (read-only comparison)
//
// Handles special cases for float64 including NaN and infinity values.
func (d *Distribution) compareValues(a, b interface{}) int {
	switch d.featureType {
	case IntegerFeature:
		aVal := a.(int64)
		bVal := b.(int64)
		if aVal < bVal {
			return -1
		} else if aVal > bVal {
			return 1
		}
		return 0

	case FloatFeature:
		aVal := a.(float64)
		bVal := b.(float64)

		// Handle NaN cases: NaN sorts to the end
		if math.IsNaN(aVal) && math.IsNaN(bVal) {
			return 0
		}
		if math.IsNaN(aVal) {
			return 1
		}
		if math.IsNaN(bVal) {
			return -1
		}

		// Handle infinity cases
		if math.IsInf(aVal, 0) || math.IsInf(bVal, 0) {
			if aVal < bVal {
				return -1
			} else if aVal > bVal {
				return 1
			}
			return 0
		}

		// Regular comparison for normal float values
		if aVal < bVal {
			return -1
		} else if aVal > bVal {
			return 1
		}
		return 0

	case StringFeature:
		aVal := a.(string)
		bVal := b.(string)
		if aVal < bVal {
			return -1
		} else if aVal > bVal {
			return 1
		}
		return 0

	default:
		logger.Fatal(fmt.Sprintf("unsupported feature type for comparison: %v", d.featureType))
		return 0
	}
}

// FeatureInfo holds metadata about a single feature from training data.
//
// Args: Created during data loading process with unified distribution
// Security: Contains training data distribution (no PII directly)
// Performance: O(1) lookups for metadata, distribution operations depend on entry count
// Relationships: Created during data loading, used throughout training process
// Side effects: Distribution updates affect memory usage based on feature cardinality
//
// Uses single Distribution for all feature types to maintain consistency and
// eliminate type conversion overhead from previous string-key approach.
type FeatureInfo struct {
	Name         string        // Feature name (CSV column header)
	Type         FeatureType   // Internal processing type (integer/float/string)
	OriginalType string        // Original YAML type specification (nominal/numeric/etc.)
	Distribution *Distribution // Unified distribution with sorted entries by value
}

// NewFeatureInfo creates a new FeatureInfo with unified distribution for any feature type.
//
// Args:
// - name: Feature name (must be non-empty CSV column header)
// - featureType: Internal processing type (IntegerFeature/FloatFeature/StringFeature)
// - originalType: Original YAML type specification (nominal/numeric/datetime/etc.)
//
// Returns: initialized FeatureInfo with appropriate distribution type
// Security: No validation of name content (assumes pre-validated by loader)
// Relationships: Used by data loading process to create feature metadata
// Side effects: Allocates memory for Distribution structure, logs fatal on invalid type
//
// Example: NewFeatureInfo("age", IntegerFeature, "numeric")
func NewFeatureInfo(name string, featureType FeatureType, originalType string) *FeatureInfo {
	return &FeatureInfo{
		Name:         name,
		Type:         featureType,
		OriginalType: originalType,
		Distribution: NewDistribution(featureType),
	}
}

// IsNumerical returns true if the feature type is numeric (int or float).
//
// Args: None (method receiver)
//
// Returns: true for IntegerFeature or FloatFeature, false otherwise
// Performance: O(1) type check operation
// Relationships: Used by splitting algorithms to determine strategy (threshold vs value-based)
func (fi *FeatureInfo) IsNumerical() bool {
	return fi.Type == IntegerFeature || fi.Type == FloatFeature
}

// IsCategorical returns true if the feature type is categorical (string).
//
// Args: None (method receiver)
//
// Returns: true for StringFeature, false otherwise
// Performance: O(1) type check operation
// Relationships: Used for categorical splitting and missing value handling
func (fi *FeatureInfo) IsCategorical() bool {
	return fi.Type == StringFeature
}

// AddValue adds a value to the distribution maintaining sorted order by value.
//
// Args:
// - value: Feature value to add (int64 for IntegerFeature, float64 for FloatFeature, string for StringFeature)
//
// Performance: O(log n) binary search + O(n) insertion to maintain sort order
// Relationships: Called during Dataset loading for each training sample
// Side effects: Updates distribution entries, may trigger memory reallocation
//
// Example: fi.AddValue(int64(25)) or fi.AddValue("red")
func (fi *FeatureInfo) AddValue(value interface{}) {
	fi.Distribution.AddValue(value)
}

// GetUniqueValueCount returns the number of unique values for this feature.
//
// Args: None (method receiver)
//
// Returns: count of unique values in distribution
// Performance: O(1) operation using distribution entries length
// Relationships: Used for feature cardinality analysis and splitting decisions
func (fi *FeatureInfo) GetUniqueValueCount() int {
	return fi.Distribution.UniqueValues()
}

// GetTotalSamples returns the total number of samples for this feature.
//
// Args: None (method receiver)
//
// Returns: total count of all values in distribution
// Performance: O(1) using cached total count
// Relationships: Used for statistical calculations and validation
func (fi *FeatureInfo) GetTotalSamples() int {
	return fi.Distribution.TotalCount()
}

// GetMostCommonValue returns the most frequently occurring value and its count.
//
// Args: None (method receiver)
//
// Returns:
// - interface{}: most common value (nil if no values, maintains original type)
// - int: count of occurrences (0 if no values recorded)
//
// Performance: O(n) iteration through distribution entries
// Relationships: Used for missing value imputation strategies
// Side effects: None (read-only operation on distribution)
func (fi *FeatureInfo) GetMostCommonValue() (interface{}, int) {
	return fi.Distribution.GetMostCommonValue()
}

// GetNumericalRangeCount returns count of numerical values in range [minValue, maxValue] for numerical features.
//
// Args:
// - minValue: minimum value (inclusive range bound, converted to appropriate type)
// - maxValue: maximum value (inclusive range bound, converted to appropriate type)
//
// Returns: count of values within specified range
// Constraints: Only valid for numerical features (IntegerFeature/FloatFeature)
// Performance: O(log n) binary search to find range bounds + O(k) summation where k is range size
// Relationships: Used by C4.5 split evaluation for threshold-based splits
// Side effects: Logs fatal if called on non-numerical feature
//
// Example: fi.GetNumericalRangeCount(20.0, 50.0) returns count between 20 and 50
func (fi *FeatureInfo) GetNumericalRangeCount(minValue, maxValue float64) int {
	if !fi.IsNumerical() {
		logger.Fatal("GetNumericalRangeCount only valid for numerical features")
		return 0
	}

	var minVal, maxVal interface{}

	// Convert bounds to appropriate type for this feature
	if fi.Type == IntegerFeature {
		minVal = int64(minValue)
		maxVal = int64(maxValue)
	} else {
		minVal = minValue
		maxVal = maxValue
	}

	startIdx := fi.Distribution.FindSplitPoint(minVal)
	endIdx := fi.Distribution.FindSplitPoint(maxVal)

	// Include the maxValue if it exists (for inclusive range)
	if endIdx < len(fi.Distribution.entries) {
		if fi.Distribution.compareValues(fi.Distribution.entries[endIdx].Value, maxVal) == 0 {
			endIdx++
		}
	}

	return fi.Distribution.GetRangeCount(startIdx, endIdx)
}

// GetSortedEntries returns all entries sorted by value for iteration and analysis.
//
// Args: None (method receiver)
//
// Returns: copy of sorted value entries (safe for external modification)
// Performance: O(n) copy operation to protect internal distribution state
// Relationships: Used by split evaluation algorithms for threshold calculations and analysis
// Side effects: Creates defensive copy to prevent external modification
//
// Example: entries := fi.GetSortedEntries() for split threshold analysis
func (fi *FeatureInfo) GetSortedEntries() []ValueEntry {
	return fi.Distribution.GetEntries()
}
