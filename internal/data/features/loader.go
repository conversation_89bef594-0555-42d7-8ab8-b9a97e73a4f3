// Package features provides YAML feature metadata loading capabilities.
//
// Loads only name and type values for each feature as specified in project
// requirements. Handles parsing and validation of YAML feature information
// files according to <PERSON><PERSON>berri specifications.
//
// Security: Validates file paths and content structure
// Performance: Efficient YAML parsing with early validation failures
package features

import (
	"fmt"
	"strings"

	"github.com/berrijam/mulberri/internal/config"
	"github.com/berrijam/mulberri/internal/io/formats/yaml"
	"github.com/berrijam/mulberri/internal/utils/logger"
)

// YAMLFeatureInfo represents feature metadata from YAML configuration.
//
// Args: YAML unmarshaling (struct tags define mapping)
// Constraints: Both Type and HandleAs are required fields
// Security: No sensitive data storage
// Relationships: Maps to project-specified YAML format
// Side effects: Used for unmarshaling YAML content into Go structs
//
// Example YAML:
//
//	weather:
//	  type: nominal
//	  handle_as: string
type YAMLFeatureInfo struct {
	Type     string `yaml:"type"`      // Feature type from project spec (nominal/numeric/etc.)
	HandleAs string `yaml:"handle_as"` // Core type conversion target (integer/float/string)
}

// YAMLFeatureConfig maps feature names to their YAML definitions.
//
// Args: Map operations for feature metadata access
// Security: Contains metadata only, no sensitive training data
// Performance: O(1) feature lookups by name
// Relationships: Output of YAML parsing, input to data loading process
type YAMLFeatureConfig map[string]YAMLFeatureInfo

// FeatureLoader handles parsing and validation of YAML feature information files.
//
// Args: File path operations for YAML processing
// Security: Validates file paths and content structure
// Performance: Stateless design enables concurrent usage
// Relationships: Used by data loading pipeline to get feature specifications
// Side effects: Reads files from disk, validates content structure
type FeatureLoader struct{}

// NewFeatureLoader creates a new YAML feature loader instance.
//
// Args: None
// Returns: loader ready for parsing YAML files
// Security: Stateless design prevents state corruption
// Performance: No initialization overhead
func NewFeatureLoader() *FeatureLoader {
	return &FeatureLoader{}
}

// LoadFeatureInfo parses a YAML feature information file and loads name/type values.
//
// Args:
// - filePath: Path to YAML feature info file (must exist and be readable)
//
// Returns:
// - *YAMLFeatureConfig: parsed configuration mapping features to metadata
//
// Security: Validates file path and content structure
// Performance: Efficient YAML parsing with early error detection
// Relationships: Primary interface for loading feature specifications
// Side effects: Reads file from disk, validates against project spec, exits on error
//
// Example YAML format from project spec:
//
//	weather:
//	  type: nominal
//	  handle_as: string
//	temperature:
//	  type: numeric
//	  handle_as: float
//	age:
//	  type: numeric
//	  handle_as: integer
//	played_on:
//	  type: datetime
//	  handle_as: integer
//	play_tennis:
//	  type: binary
//	  handle_as: string
func (fl *FeatureLoader) LoadFeatureInfo(filePath string) *YAMLFeatureConfig {
	if fl == nil {
		logger.Fatal("FeatureLoader is nil")
	}

	// Parse YAML content using the yaml parser (includes file validation)
	var config YAMLFeatureConfig
	yaml.ParseFile(filePath, &config)

	// Validate parsed configuration against project specifications
	fl.validateConfiguration(config)

	return &config
}

// validateConfiguration validates overall configuration structure and individual features.
//
// Args:
// - config: Parsed YAML configuration to validate
//
// Side effects: Exits if configuration is invalid
// Security: Validates against project-specified constraints
// Performance: Iterates through all features for validation
// Side effects: Validates each feature in configuration
func (fl *FeatureLoader) validateConfiguration(config YAMLFeatureConfig) {
	if fl == nil {
		logger.Fatal("FeatureLoader is nil")
	}

	if config == nil {
		logger.Fatal("configuration is nil")
	}

	if len(config) == 0 {
		logger.Fatal("no features defined in configuration")
	}

	// Validate each feature against project specifications
	for featureName, featureInfo := range config {
		fl.validateFeature(featureName, featureInfo)
	}
}

// validateFeature validates a single feature's YAML configuration.
//
// Args:
// - featureName: Name of feature being validated (must be non-empty)
// - featureInfo: YAML feature configuration to validate
//
// Side effects: Exits if feature is invalid
// Security: Validates against supported types from config package
// Performance: Multiple validation checks per feature
// Relationships: Uses config.SupportedFeatureTypes and config.SupportedHandleAsTypes
// Side effects: Comprehensive validation of feature specification
func (fl *FeatureLoader) validateFeature(featureName string, featureInfo YAMLFeatureInfo) {
	if fl == nil {
		logger.Fatal("FeatureLoader is nil")
	}

	// Validate feature name
	if strings.TrimSpace(featureName) == "" {
		logger.Fatal(fmt.Sprintf("feature name cannot be empty or whitespace-only for feature '%s'", featureName))
	}

	// Validate type field against project specifications
	fl.validateFeatureType(featureInfo.Type)

	// Validate handle_as field against project specifications
	fl.validateHandleAs(featureInfo.HandleAs)

	// Validate type and handle_as compatibility
	fl.validateTypeCompatibility(featureInfo.Type, featureInfo.HandleAs)

}

// validateFeatureType validates that feature type is supported by project spec.
//
// Args:
// - featureType: Type string to validate
//
// Constraints: Must be in config.SupportedFeatureTypes list
// Relationships: References supported types from config package
// Performance: Linear search through supported types list
// Side effects: Exits on validation error
func (fl *FeatureLoader) validateFeatureType(featureType string) {
	if fl == nil {
		logger.Fatal("FeatureLoader is nil")
	}

	normalizedType := strings.ToLower(strings.TrimSpace(featureType))

	if normalizedType == "" {
		logger.Fatal("type field is required")
	}

	// Check against supported types from project specification
	for _, supportedType := range config.SupportedFeatureTypes {
		if normalizedType == supportedType {
			return
		}
	}

	logger.Fatal(fmt.Sprintf("unsupported type '%s', must be one of: %v",
		featureType, config.SupportedFeatureTypes))
}

// validateHandleAs validates that handle_as value is supported by project spec.
//
// Args:
// - handleAs: Handle_as string to validate
//
// Constraints: Must be in config.SupportedHandleAsTypes list
// Relationships: References supported handle_as types from config package
// Performance: Linear search through supported handle_as list
// Side effects: Exits on validation error
func (fl *FeatureLoader) validateHandleAs(handleAs string) {
	if fl == nil {
		logger.Fatal("FeatureLoader is nil")
	}

	normalizedHandleAs := strings.ToLower(strings.TrimSpace(handleAs))

	if normalizedHandleAs == "" {
		logger.Fatal("handle_as field is required")
	}

	// Check against supported handle_as types from project specification
	for _, supportedHandleAs := range config.SupportedHandleAsTypes {
		if normalizedHandleAs == supportedHandleAs {
			return
		}
	}

	logger.Fatal(fmt.Sprintf("unsupported handle_as '%s', must be one of: %v",
		handleAs, config.SupportedHandleAsTypes))
}

// ConvertToFeatureTypeMap converts YAML feature configuration to FeatureType map for dataset loading.
//
// Args:
// - yamlConfig: Parsed YAML configuration mapping feature names to metadata
//
// Returns:
// - map[string]FeatureType: Map of feature names to internal FeatureType enums
//
// Security: Validates all handle_as values during conversion
// Performance: O(n) where n is number of features in configuration
// Relationships: Bridges YAML configuration and dataset loading functionality
// Side effects: Calls logger.Fatal on invalid configurations, terminating the application
//
// Example:
//
//	yamlConfig := YAMLFeatureConfig{
//	    "age": YAMLFeatureInfo{Type: "numeric", HandleAs: "integer"},
//	    "name": YAMLFeatureInfo{Type: "nominal", HandleAs: "string"},
//	}
//	featureTypes := ConvertToFeatureTypeMap(&yamlConfig)
//	// Result: map[string]FeatureType{"age": IntegerFeature, "name": StringFeature}
func ConvertToFeatureTypeMap(yamlConfig *YAMLFeatureConfig) map[string]FeatureType {
	if yamlConfig == nil {
		logger.Fatal("yamlConfig cannot be nil")
		return nil
	}

	featureTypes := make(map[string]FeatureType, len(*yamlConfig))

	for featureName, featureInfo := range *yamlConfig {
		featureType := ParseFeatureType(featureInfo.HandleAs)
		featureTypes[featureName] = featureType
		logger.Debug(fmt.Sprintf("Feature: %s -> %s", featureName, featureType.String()))
	}

	return featureTypes
}

// validateTypeCompatibility validates logical compatibility between type and handle_as.
//
// Args:
// - featureType: Feature type string (normalized)
// - handleAs: Handle_as string (normalized)
//
// Constraints: Prevents illogical combinations (e.g., nominal->float)
// Performance: Map lookup for incompatibility rules
// Side effects: Exits on incompatible combinations
func (fl *FeatureLoader) validateTypeCompatibility(featureType, handleAs string) {
	if fl == nil {
		logger.Fatal("FeatureLoader is nil")
	}

	normalizedType := strings.ToLower(strings.TrimSpace(featureType))
	normalizedHandleAs := strings.ToLower(strings.TrimSpace(handleAs))

	// Define clearly incompatible combinations based on logical constraints
	incompatibleCombinations := map[string][]string{
		"nominal": {"float"}, // Nominal features shouldn't be float
		"binary":  {"float"}, // Binary features shouldn't be float
	}

	if incompatibleTypes, exists := incompatibleCombinations[normalizedType]; exists {
		for _, incompatibleType := range incompatibleTypes {
			if normalizedHandleAs == incompatibleType {
				logger.Fatal(fmt.Sprintf("type '%s' is not compatible with handle_as '%s'",
					featureType, handleAs))
			}
		}
	}

	// All other combinations allowed for flexibility
}

// ValidateCSVColumnsAgainstFeatureInfo validates CSV columns against feature information.
//
// Args:
// - config: Parsed feature info configuration
// - csvHeaders: Column headers from CSV file
// - targetColumn: Target column name to exclude from validation
//
// Security: Validates data structure consistency
// Performance: O(n) iteration with hash map lookups
// Relationships: Used during training to allow target column metadata
// Side effects: Validates feature-to-column mapping, exits on error
//
// Allows target column to be defined in feature info without requiring it to be a feature.
// Useful for training scenarios where target column has metadata but isn't a feature.
// Tracks:
// - Features defined in FeatureInfo but NOT in CSV
// - Features in CSV but NOT defined in FeatureInfo (excluding target column)
func ValidateCSVColumnsAgainstFeatureInfo(config *YAMLFeatureConfig, csvHeaders []string, targetColumn string) {
	if config == nil {
		logger.Fatal("feature config is nil")
		return
	}

	// Create map of CSV headers for efficient lookup
	csvHeaderMap := make(map[string]bool)
	for _, header := range csvHeaders {
		csvHeaderMap[header] = true
	}

	// Create map of feature info for efficient lookup
	featureInfoMap := make(map[string]bool)
	for featureName := range *config {
		featureInfoMap[featureName] = true
	}

	// Track features defined in FeatureInfo but NOT in CSV
	var featuresInInfoNotInCSV []string
	for featureName := range *config {
		if !csvHeaderMap[featureName] {
			featuresInInfoNotInCSV = append(featuresInInfoNotInCSV, featureName)
		}
	}

	// Track features in CSV but NOT defined in FeatureInfo (excluding target column)
	var featuresInCSVNotInInfo []string
	for _, header := range csvHeaders {
		// Skip target column if specified
		if targetColumn != "" && header == targetColumn {
			continue
		}
		if !featureInfoMap[header] {
			featuresInCSVNotInInfo = append(featuresInCSVNotInInfo, header)
		}
	}

	// Log informational message about features in CSV but not in FeatureInfo
	if len(featuresInCSVNotInInfo) > 0 {
		logger.Info(fmt.Sprintf("Features found in CSV but NOT defined in FeatureInfo: %v", featuresInCSVNotInInfo))
	}

	// Report critical error and exit if features defined in FeatureInfo are missing from CSV
	if len(featuresInInfoNotInCSV) > 0 {
		logger.Fatal(fmt.Sprintf("Features defined in FeatureInfo but NOT found in CSV: %v", featuresInInfoNotInCSV))
	}
}
