// Package integration provides integration tests for the core data structures
// with the CLI and workflow components.
package integration

import (
	"os"
	"path/filepath"
	"testing"

	"github.com/berrijam/mulberri/internal/data/dataset"
	"github.com/berrijam/mulberri/internal/data/features"
)

// TestFeatureTypeConversion tests the conversion from YAML configuration to FeatureType map.
func TestFeatureTypeConversion(t *testing.T) {
	// Create test YAML configuration
	yamlConfig := features.YAMLFeatureConfig{
		"age": features.YAMLFeatureInfo{
			Type:     "numeric",
			HandleAs: "integer",
		},
		"salary": features.YAMLFeatureInfo{
			Type:     "numeric",
			HandleAs: "float",
		},
		"department": features.YAMLFeatureInfo{
			Type:     "nominal",
			HandleAs: "string",
		},
	}

	// Convert to FeatureType map
	featureTypes, err := features.ConvertToFeatureTypeMap(&yamlConfig)
	if err != nil {
		t.Fatalf("Failed to convert YAML config to FeatureType map: %v", err)
	}

	// Verify conversions
	expectedTypes := map[string]features.FeatureType{
		"age":        features.IntegerFeature,
		"salary":     features.FloatFeature,
		"department": features.StringFeature,
	}

	if len(featureTypes) != len(expectedTypes) {
		t.Errorf("Expected %d features, got %d", len(expectedTypes), len(featureTypes))
	}

	for featureName, expectedType := range expectedTypes {
		if actualType, exists := featureTypes[featureName]; !exists {
			t.Errorf("Feature '%s' not found in converted map", featureName)
		} else if actualType != expectedType {
			t.Errorf("Feature '%s': expected %s, got %s", featureName, expectedType.String(), actualType.String())
		}
	}
}

// TestCSVToDatasetIntegration tests the end-to-end CSV loading with type conversion.
func TestCSVToDatasetIntegration(t *testing.T) {
	// Create temporary CSV file
	tempDir := t.TempDir()
	csvPath := filepath.Join(tempDir, "test_data.csv")

	csvContent := `name,age,salary,active
John,25,50000.5,true
Jane,30,75000.0,false
Bob,35,60000.25,true`

	err := os.WriteFile(csvPath, []byte(csvContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create test CSV file: %v", err)
	}

	// Define feature types
	featureTypes := map[string]features.FeatureType{
		"name":   features.StringFeature,
		"age":    features.IntegerFeature,
		"salary": features.FloatFeature,
	}

	// Load CSV to typed dataset
	typedDataset, err := dataset.LoadCSVToDataset[string](csvPath, featureTypes, "active")
	if err != nil {
		t.Fatalf("Failed to load CSV to typed dataset: %v", err)
	}

	// Verify dataset properties
	if typedDataset.GetRowCount() != 3 {
		t.Errorf("Expected 3 rows, got %d", typedDataset.GetRowCount())
	}

	featureOrder := typedDataset.GetFeatureOrder()
	if len(featureOrder) != 3 {
		t.Errorf("Expected 3 features, got %d", len(featureOrder))
	}

	// Verify column types
	expectedFeatures := []string{"name", "age", "salary"}
	for _, featureName := range expectedFeatures {
		column, err := typedDataset.GetColumn(featureName)
		if err != nil {
			t.Errorf("Failed to get column '%s': %v", featureName, err)
			continue
		}

		expectedType := featureTypes[featureName]
		if column.GetType() != expectedType {
			t.Errorf("Feature '%s': expected type %s, got %s", 
				featureName, expectedType.String(), column.GetType().String())
		}

		// Verify numerical properties
		switch expectedType {
		case features.IntegerFeature, features.FloatFeature:
			if !column.IsNumerical() {
				t.Errorf("Feature '%s' should be numerical", featureName)
			}
		case features.StringFeature:
			if column.IsNumerical() {
				t.Errorf("Feature '%s' should not be numerical", featureName)
			}
		}
	}
}

// TestParseFeatureType tests the ParseFeatureType function with various inputs.
func TestParseFeatureType(t *testing.T) {
	tests := []struct {
		name        string
		handleAs    string
		expected    features.FeatureType
		expectError bool
	}{
		{
			name:        "integer handle_as",
			handleAs:    "integer",
			expected:    features.IntegerFeature,
			expectError: false,
		},
		{
			name:        "float handle_as",
			handleAs:    "float",
			expected:    features.FloatFeature,
			expectError: false,
		},
		{
			name:        "string handle_as",
			handleAs:    "string",
			expected:    features.StringFeature,
			expectError: false,
		},
		{
			name:        "case insensitive - INTEGER",
			handleAs:    "INTEGER",
			expected:    features.IntegerFeature,
			expectError: false,
		},
		{
			name:        "with whitespace",
			handleAs:    "  float  ",
			expected:    features.FloatFeature,
			expectError: false,
		},
		{
			name:        "invalid handle_as",
			handleAs:    "invalid",
			expected:    features.StringFeature, // default return value
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := features.ParseFeatureType(tt.handleAs)

			if tt.expectError {
				if err == nil {
					t.Errorf("Expected error for handle_as '%s', but got none", tt.handleAs)
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error for handle_as '%s': %v", tt.handleAs, err)
				}
				if result != tt.expected {
					t.Errorf("Expected %s, got %s", tt.expected.String(), result.String())
				}
			}
		})
	}
}
